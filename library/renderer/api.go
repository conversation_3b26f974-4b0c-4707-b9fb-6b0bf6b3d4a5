package renderer

import (
	"fmt"
	"strconv"

	"golang.org/x/sync/errgroup"

	"dt-common/logger"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-cmanager/library/renderer/common"
	"redis-cmanager/library/renderer/lib"
)

// ===========================================
//               变更interface
// ===========================================

// 更新全组件白名单
func EnsureWhitelists(deployment *Cluster) error {
	g := errgroup.Group{}
	g.Go(func() error {
		redisInstances, err := lib.QueryRedisInstances(deployment.Name, true)
		if err != nil {
			return err
		}
		err = ensureWhitelist(deployment, common.COMPONENT_REDIS, redisInstances)
		if err != nil {
			return err
		}
		return nil
	})
	g.Go(func() error {
		sentinelInstances, err := lib.QuerySentinelInstances(deployment.Name, true)
		if err != nil {
			return err
		}
		err = ensureWhitelist(deployment, common.COMPONENT_SENTINEL, sentinelInstances)
		if err != nil {
			return err
		}
		return nil
	})
	g.Go(func() error {
		proxyInstances, err := lib.QueryProxyInstances(deployment.Name, true)
		if err != nil {
			return err
		}
		err = ensureWhitelist(deployment, common.COMPONENT_ROUTER, proxyInstances)
		if err != nil {
			return err
		}
		return nil
	})
	if err := g.Wait(); err != nil {
		return err
	}

	return nil
}

// ===========================================
//               检查interface
// ===========================================

// 检查是否存在打着污点但没有屏蔽的Proxy
func IsThereTaintedProxy(deployment *Cluster) (bool, error) {
	// 获取proxy pod list
	podList, err := lib.ListPods(deployment.Name, common.COMPONENT_ROUTER, true)
	if err != nil {
		return false, err
	}
	for _, pod := range podList {
		if pod.Labels[common.LABEL_TAINT] == "true" {
			return true, nil
		}
	}

	return false, nil
}

// CheckMasterSlaveConnections 检查主从连接状态
// 遍历集群中的主库，检查每个主库是否有足够数量的从库
func CheckMasterSlaveConnections(clusterName string) error {
	// 获取deployment配置
	deployment, err := GetDeploymentFromDB(clusterName)
	if err != nil {
		logger.Error("failed to get deployment from db, cluster=%s, error=(%v)", clusterName, err)
		return err
	}

	// 计算预期的从库数量
	expectedSlaves := 0
	for _, replica := range deployment.Spec.Redis.Replicas {
		expectedSlaves += replica
	}

	// 获取所有Redis实例（不包含污点pod）
	podList, err := lib.ListPods(clusterName, common.COMPONENT_REDIS, false)
	if err != nil {
		logger.Error("failed to list redis pods, cluster=%s, error=(%v)", clusterName, err)
		return err
	}

	masterCount := 0
	// 遍历所有pod，找到主库并检查从库数量
	for _, pod := range podList {
		// 获取Redis info信息
		info, err := redisc.Info(pod.PodIp, deployment.Spec.Redis.Port, redisc.REPLICATION)
		if err != nil {
			logger.Warn("failed to get redis info, cluster=%s, pod=%s, error=(%v)", clusterName, pod.PodName, err)
			continue
		}

		// 只检查主库
		if info["role"] != omodel.REDIS_ROLE_MASTER {
			continue
		}

		masterCount++

		// 检查主库的connected_slaves数量
		connectedSlavesStr, exists := info["connected_slaves"]
		if !exists {
			return fmt.Errorf("connected_slaves not found in master %s info", pod.PodName)
		}

		connectedSlaves, err := strconv.Atoi(connectedSlavesStr)
		if err != nil {
			return fmt.Errorf("failed to parse connected_slaves for master %s, value=%s", pod.PodName, connectedSlavesStr)
		}

		// 检查从库数量是否符合预期
		// 在替换过程中，污点从库还未删除，所以connected_slaves应该大于等于expectedSlaves
		if connectedSlaves < expectedSlaves {
			return fmt.Errorf("master %s has %d connected slaves, expected at least %d", pod.PodName, connectedSlaves, expectedSlaves)
		}

		logger.Info("master %s has %d connected slaves (expected: %d, includes old tainted slaves)", pod.PodName, connectedSlaves, expectedSlaves)
	}

	// 检查主库数量是否符合预期
	if masterCount != deployment.Spec.Redis.NumOfShards {
		return fmt.Errorf("found %d masters, expected %d", masterCount, deployment.Spec.Redis.NumOfShards)
	}

	logger.Info("all %d masters have sufficient slave connections, cluster=%s", masterCount, clusterName)
	return nil
}
