/*
 * @Author: jiayiming
 * @Date: 2025-01-03 16:36
 * @Last Modified by: jiayiming
 * @Description: Remove模块提供实例删除接口，作为Render()只增不减的补充
 *  扩缩容替换从库时需要先给从库打污点，实现先上线新pod，再下线旧pod
 *  实例宕机后，会给宕机的实例打污点，实现替换
 */
package renderer

import (
	"fmt"
	"sort"

	"dt-common/logger"
	"dt-common/noah"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-cmanager/library/fec"
	"redis-cmanager/library/renderer/common"
	"redis-cmanager/library/renderer/lib"
)

// ===========================================
// 				   手动删除
// ===========================================

// 根据podName打污点
func TaintPods(clusterName string, podNames []string, taint bool) error {
	// 根据name分组
	nameMap := map[string]map[string]int{common.COMPONENT_REDIS: {}, common.COMPONENT_ROUTER: {}, common.COMPONENT_SENTINEL: {}}
	for _, name := range podNames {
		switch name[0:2] {
		case "r-":
			nameMap[common.COMPONENT_REDIS][name] = 1
		case "s-":
			nameMap[common.COMPONENT_SENTINEL][name] = 1
		case "p-":
			nameMap[common.COMPONENT_ROUTER][name] = 1
		default:
			return fmt.Errorf("pod name %s sames not a valid pod", name)
		}
	}

	// 获取该集群对应组件的所有pod
	podToOpt := []*fec.PodInfo{}
	for component, names := range nameMap {
		if len(names) == 0 {
			continue
		}

		podList, err := lib.ListPods(clusterName, component, true)
		if err != nil {
			return err
		}

		for _, pod := range podList {
			if _, ok := names[pod.PodName]; ok {
				podToOpt = append(podToOpt, pod)
			}
		}
	}
	if len(podToOpt) == 0 {
		return fmt.Errorf("no pod matched")
	}

	// step1. k8s pod打污点
	err := lib.TaintPods(podToOpt, taint)
	if err != nil {
		return err
	}

	// step2. 数据库实例打污点
	err = lib.TaintFromPods(clusterName, podToOpt, taint)
	if err != nil {
		return err
	}

	return nil
}

// 删除Pods
func DeletePods(clusterName string, podNames []string, force bool) error {
	// 根据name分组
	nameMap := map[string]map[string]int{common.COMPONENT_REDIS: {}, common.COMPONENT_ROUTER: {}, common.COMPONENT_SENTINEL: {}}
	for _, name := range podNames {
		switch name[0:2] {
		case "r-":
			nameMap[common.COMPONENT_REDIS][name] = 1
		case "s-":
			nameMap[common.COMPONENT_SENTINEL][name] = 1
		case "p-":
			nameMap[common.COMPONENT_ROUTER][name] = 1
		default:
			return fmt.Errorf("pod name %s sames not a valid pod", name)
		}
	}

	// 获取该集群对应组件的所有pod
	podToDelete := []*fec.PodInfo{}
	for component, names := range nameMap {
		if len(names) == 0 {
			continue
		}

		podList, err := lib.ListPods(clusterName, component, true)
		if err != nil {
			return err
		}

		for _, pod := range podList {
			if _, ok := names[pod.PodName]; !ok {
				continue
			}
			if pod.Labels[common.LABEL_TAINT] != "true" {
				return fmt.Errorf("pod %s is not a tainted pod, cannot be deleted", pod.PodName)
			}
			podToDelete = append(podToDelete, pod)
		}
	}
	if len(podToDelete) == 0 {
		return fmt.Errorf("no pod matched")
	}

	// 删除pod
	err := lib.DeletePods(podToDelete, force)
	if err != nil {
		return err
	}

	// 清理数据库
	err = lib.DeleteFromPods(clusterName, podToDelete)
	if err != nil {
		return err
	}

	return nil
}

// ===========================================
// 				   自动删除
// ===========================================

// 删除打了污点的实例
func deleteTaintedPods(clusterName string, components ...string) error {
	if len(components) == 0 {
		components = []string{common.COMPONENT_REDIS, common.COMPONENT_ROUTER, common.COMPONENT_SENTINEL}
	}

	podToDelete := []*fec.PodInfo{}
	for _, component := range components {
		podList, err := lib.ListPods(clusterName, component, true)
		if err != nil {
			return err
		}

		// 找到tainted pods
		for _, pod := range podList {
			if pod.Labels[common.LABEL_TAINT] == "true" {
				podToDelete = append(podToDelete, pod)
			}
		}
	}

	// 删除pod
	err := lib.DeletePods(podToDelete)
	if err != nil {
		return err
	}

	// 清理数据库
	err = lib.DeleteFromPods(clusterName, podToDelete)
	if err != nil {
		return err
	}

	return nil
}

// 删除污点Proxy
func DeleteTaintedProxy(cluster string) error {
	return deleteTaintedPods(cluster, common.COMPONENT_ROUTER)
}

// 删除污点Redis
func DeleteTaintedRedis(cluster string) error {
	return deleteTaintedPods(cluster, common.COMPONENT_REDIS)
}

// ===========================================
// 				   Proxy缩容
// ===========================================

// 缩容后，需要对超出数量的实例打污点，否则会阻塞render流程
func LabelProxyToScaleDown(deployment *Cluster, proxyNumPerIDC int) error {
	// 获取Pods，将数组转成map方便后续比对
	untaintPodMap := make(map[string]*fec.PodInfo)
	podList, err := lib.ListPods(deployment.Name, common.COMPONENT_ROUTER)
	if err != nil {
		return err
	}
	for _, pod := range podList {
		untaintPodMap[pod.PodIp] = pod
	}

	// 获取noah实例列表
	noahInstances, err := noah.GetInstancesV2(deployment.Spec.App.ProductLine, common.GetRouterAppName(deployment.Spec.App.AppPrefix))
	if err != nil {
		return err
	}

	// 转换成map方便比对，[机房][屏蔽状态][]noah.Instance
	instanceToDisable := []string{}
	insMap := make(map[string][]*noah.Instance)
	for _, instance := range noahInstances {
		// 过滤掉已经被打了污点的实例
		if _, ok := untaintPodMap[instance.IP]; !ok {
			// 如果已经被打上了污点却没有屏蔽，就给屏蔽上
			if !instance.Disable {
				instanceToDisable = append(instanceToDisable, instance.Name)
			}
			continue
		}

		insMap[instance.Tags["idc"]] = append(insMap[instance.Tags["idc"]], instance)
	}

	// 分机房进行处理
	podToTaint := make([]*fec.PodInfo, 0, len(insMap))
	for _, instances := range insMap {
		instanceCount := len(instances)
		// 不需要打污点
		if len(instances) <= proxyNumPerIDC {
			continue
		}

		// Name从大到小排序，把屏蔽了的实例放前面
		sort.Slice(instances, func(i, j int) bool {
			if instances[i].Disable != instances[j].Disable {
				return instances[i].Disable
			}
			return instances[i].Name > instances[j].Name
		})

		// 需要打污点的实例数量
		remainToTaint := len(instances) - proxyNumPerIDC
		for i := 0; i < instanceCount && remainToTaint > 0; i++ {
			instance := instances[i]
			pod, ok := untaintPodMap[instance.IP]
			if !ok {
				logger.Error("proxy exist in noah but not in fec, cluster=%s, ip=%s", deployment.Name, instance.IP)
				return fmt.Errorf("proxy exist in noah but not in fec")
			}
			podToTaint = append(podToTaint, pod)
			if !instance.Disable {
				instanceToDisable = append(instanceToDisable, instance.Name)
			}
			remainToTaint--
		}
	}

	// noah屏蔽
	if len(instanceToDisable) != 0 {
		err = noah.DisableInstances(deployment.Spec.App.ProductLine, common.GetRouterAppName(deployment.Spec.App.AppPrefix), instanceToDisable)
		if err != nil {
			return err
		}
	}

	// 污点
	if len(podToTaint) != 0 {
		// step1. k8s pod打污点
		err = lib.TaintPods(podToTaint, true)
		if err != nil {
			return err
		}

		// step2. 数据库实例打污点
		err = lib.TaintFromPods(deployment.Name, podToTaint, true)
		if err != nil {
			return err
		}
	}

	return nil
}

// ===========================================
// 				   替换从库
// ===========================================

// 替换从库打污点，将所有与规格不匹配的从库打上污点
func LabelRedisToReplace(clusterName string) error {
	deployment, err := GetDeploymentFromDB(clusterName)
	if err != nil {
		return err
	}
	// 目标Pod内存
	tarPodMem := float64(deployment.Spec.Redis.Resource.Mem)

	podToTaint := []*fec.PodInfo{}
	podList, err := lib.ListPods(clusterName, common.COMPONENT_REDIS, true)
	if err != nil {
		return err
	}

	// 遍历所有redis pod，检查pod规格是否符合配置要求
	for _, pod := range podList {
		for _, c := range pod.ContainerInfo {
			// 非业务容器，或者 mem和deployment.mem 一致的跳过
			if c.ContainerName != pod.PodName || c.Mem == tarPodMem {
				continue
			}

			// 获取info信息以便判断角色，实例必须处于正常状态才能继续替换变更
			info, err := redisc.Info(pod.PodIp, common.DEFAULT_REDIS_PORT, redisc.REPLICATION)
			if err != nil {
				return err
			}
			// 只有从库能打污点，主库不能打污点
			if info["role"] != omodel.REDIS_ROLE_SLAVE {
				break
			}
			// 判断是否已被打污点
			if pod.Labels[common.LABEL_TAINT] != "true" {
				podToTaint = append(podToTaint, pod)
			}
			break
		}
	}

	// 打污点
	if len(podToTaint) > 0 {
		err := lib.TaintPods(podToTaint, true)
		if err != nil {
			return err
		}
		err = lib.TaintFromPods(clusterName, podToTaint, true)
		if err != nil {
			return err
		}
	}

	return nil
}
