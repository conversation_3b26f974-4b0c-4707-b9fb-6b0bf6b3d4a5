package cluster

import (
	"fmt"
	"strconv"
	"strings"

	"dt-common/logger"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-cmanager/library/fec"
	"redis-cmanager/library/renderer"
	"redis-cmanager/library/renderer/common"
	"redis-cmanager/library/renderer/lib"
)

// 更新 deployment.spec，修改redis的maxmemory和pod mem
// 1、新申请的pod会是pod mem的规格，存量pod不会受影响
// 2、maxmemory
func MemoryScaling(stageId int64, clusterName string, shardSize int) error {
	// 1、修改spec
	deployment, err := renderer.GetDeploymentFromDB(clusterName)
	if err != nil {
		logger.Error("[STAGE %d] failed to get deployment from db", stageId, err)
		return err
	}

	// 初始化巡检状态
	deployment.InspectionMode = omodel.MODE_FULL_CARE                     // 不改成fullCare不会触发渲染
	deployment.InspectionResult.State = renderer.INSPECTION_STATE_INIT    // 改成""用于检测渲染结果
	deployment.InspectionResult.StepErrTimes = [renderer.TOTAL_STEP]int{} // 初始化阶段状态

	// 更新deployement，已经修改过的不重复创建
	podMemory := shardSize * 1024 * 2
	if deployment.Spec.Redis.Resource.Mem == podMemory {
		err = renderer.UpdateDeploymentMode(clusterName, deployment.InspectionMode)
		if err != nil {
			logger.Error("[STAGE %d] failed to change mode to fullCare, error=(%v)", stageId, err)
			return err
		}

		err := renderer.UpdateDeploymentResult(clusterName, deployment.InspectionResult)
		if err != nil {
			logger.Error("[STAGE %d] failed to init inspection results, error=(%v)", stageId, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "cluster %s maxmemory already changed to %dGB", clusterName, shardSize)
	} else {
		// 修改maxmemory和Resource.Mem
		deployment.Spec.Redis.Resource.Mem = podMemory
		for i, config := range deployment.Spec.Redis.CustomConfig {
			if strings.HasPrefix(config, "maxmemory") {
				deployment.Spec.Redis.CustomConfig[i] = fmt.Sprintf("maxmemory %vGB", shardSize)
			}
		}

		err = renderer.SaveNewDeployment(deployment)
		if err != nil {
			logger.Error("[STAGE %d] failed to save new deployment to db, error=(%v)", stageId, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "cluster %s maxmemory changed to %dGB", clusterName, shardSize)
	}

	return nil
}

// 替换从库，支持重入
// 1、规格不匹配的从库打污点
// 2、等新从库拉起来后删除掉污点从库
func ReplaceSlaves(stageId int64, clusterName string) error {
	// 1、给从库打污点（如果规格不匹配的话）
	err := renderer.LabelRedisToReplace(clusterName)
	if err != nil {
		logger.Error("[STAGE %d] failed to label redis to replace, error=(%v)", stageId, err)
		return err
	}
	omodel.StageAppendInfoLog(stageId, "cluster %s labeled slaves to replace", clusterName)

	// 2、重启render
	err = renderer.InitDeploymentRender(clusterName)
	if err != nil {
		logger.Error("[STAGE %d] failed to init deployment render, error=(%v)", stageId, err)
		return err
	}
	omodel.StageAppendInfoLog(stageId, "cluster %s initiated deployment render", clusterName)

	// 3、等新pods ready
	err = WaitForRenderSuccess(stageId, clusterName)
	if err != nil {
		logger.Error("[STAGE %d] failed to wait for render success, error=(%v)", stageId, err)
		return err
	}
	omodel.StageAppendInfoLog(stageId, "cluster %s render completed successfully", clusterName)

	// 4、二次检查ready，遍历masters，检查info中的connected_slaves数量
	err = checkMasterSlaveConnections(stageId, clusterName)
	if err != nil {
		logger.Error("[STAGE %d] failed to check master-slave connections, error=(%v)", stageId, err)
		return err
	}
	omodel.StageAppendInfoLog(stageId, "cluster %s master-slave connections verified", clusterName)

	// 5、删除污点pod
	err = renderer.DeleteTaintedRedis(clusterName)
	if err != nil {
		logger.Error("[STAGE %d] failed to delete tainted pods, error=(%v)", stageId, err)
		return err
	}
	omodel.StageAppendInfoLog(stageId, "cluster %s tainted pods deleted successfully", clusterName)

	return nil
}

// checkMasterSlaveConnections 检查主从连接状态
// 遍历所有主库，检查每个主库的connected_slaves数量是否符合预期
func checkMasterSlaveConnections(stageId int64, clusterName string) error {
	// 获取deployment配置
	deployment, err := renderer.GetDeploymentFromDB(clusterName)
	if err != nil {
		logger.Error("[STAGE %d] failed to get deployment from db, error=(%v)", stageId, err)
		return err
	}

	// 获取所有Redis实例
	podList, err := lib.ListPods(clusterName, common.COMPONENT_REDIS, false)
	if err != nil {
		logger.Error("[STAGE %d] failed to list redis pods, error=(%v)", stageId, err)
		return err
	}

	// 按分片分组检查主从关系
	for shardIndex := 0; shardIndex < deployment.Spec.Redis.NumOfShards; shardIndex++ {
		var masterPod *fec.PodInfo
		expectedSlaves := 0

		// 计算该分片预期的从库数量
		for _, replica := range deployment.Spec.Redis.Replicas {
			expectedSlaves += replica
		}

		// 找到该分片的主库
		for _, pod := range podList {
			// 跳过污点pod
			if pod.Labels[common.LABEL_TAINT] == "true" {
				continue
			}

			// 检查是否是当前分片的实例
			podIndexStr, exists := pod.Labels[common.LABEL_INDEX]
			if !exists {
				logger.Warn("[STAGE %d] pod %s has no index label", stageId, pod.PodName)
				continue
			}

			podIndex, err := strconv.Atoi(podIndexStr)
			if err != nil {
				logger.Warn("[STAGE %d] pod %s has invalid index label: %s", stageId, pod.PodName, podIndexStr)
				continue
			}

			if podIndex != shardIndex {
				continue
			}

			// 获取Redis info信息
			info, err := redisc.Info(pod.PodIp, deployment.Spec.Redis.Port, redisc.REPLICATION)
			if err != nil {
				logger.Warn("[STAGE %d] failed to get redis info, pod=%s, error=(%v)", stageId, pod.PodName, err)
				continue
			}

			// 找到主库
			if info["role"] == omodel.REDIS_ROLE_MASTER {
				masterPod = pod
				break
			}
		}

		if masterPod == nil {
			return fmt.Errorf("no master found for shard %d", shardIndex)
		}

		// 检查主库的connected_slaves数量
		info, err := redisc.Info(masterPod.PodIp, deployment.Spec.Redis.Port, redisc.REPLICATION)
		if err != nil {
			return fmt.Errorf("failed to get master info for shard %d, error=(%v)", shardIndex, err)
		}

		connectedSlavesStr, exists := info["connected_slaves"]
		if !exists {
			return fmt.Errorf("connected_slaves not found in master info for shard %d", shardIndex)
		}

		connectedSlaves, err := strconv.Atoi(connectedSlavesStr)
		if err != nil {
			return fmt.Errorf("failed to parse connected_slaves for shard %d, value=%s", shardIndex, connectedSlavesStr)
		}

		// 检查从库数量是否符合预期
		if connectedSlaves != expectedSlaves {
			return fmt.Errorf("shard %d master has %d connected slaves, expected %d", shardIndex, connectedSlaves, expectedSlaves)
		}

		omodel.StageAppendInfoLog(stageId, "shard %d master has %d connected slaves as expected", shardIndex, connectedSlaves)
	}

	return nil
}
